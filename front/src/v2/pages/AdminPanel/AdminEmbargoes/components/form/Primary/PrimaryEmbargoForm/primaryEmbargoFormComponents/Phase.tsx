import React, { useEffect, useState } from 'react'
import { DatePicker, Form, Input, Tag } from 'antd'
import { WrappedFormUtils } from 'antd/lib/form/Form'

import { englishLanguageId } from 'src/v2/constants/languages'

import Text from 'src/v2/sharedComponents/Text/Text'
import IPAPermissions3 from 'src/AdminPanel/Components/Inputs/IPAPermissions3'
import CustomButton from 'src/v2/sharedComponents/Button/Custombutton'
import { AddPhaseContainer, PhaseActions, PhaseFieldsContainer, PhaseStyled } from './PhaseStyled'
import PlusIcon from 'src/v2/assets/icons/PlusIcon'
import MinusIcon from 'src/v2/assets/icons/MinusIcon'
import { FormEmbargoPhase, PhaseRequiredFields } from '../hooks/usePrimaryEmbargoForm'
import customValidations from '../customValidations'
import HelpText from 'src/v2/sharedComponents/HelpText/HelpText'
import moment from 'moment'
import { handlePhaseChangeParams } from '../PrimaryEmbargoForm'
import Tooltip from 'src/v2/sharedComponents/Tooltip/Tooltip'

const DateTimePicker: any = DatePicker

interface PhaseProps {
  phase: FormEmbargoPhase
  index: number
  form: WrappedFormUtils
  requiredFields: () => PhaseRequiredFields[]
  canAddNewPhase: boolean
  canBeRemoved: boolean
  savePhaseHandler: (phase: FormEmbargoPhase) => void
  addPhaseHandler: (replicatePhase: boolean) => void
  removePhaseHandler: () => void
  onPhaseChange: ({ phaseIndex, isPhaseValid, isPhaseSaved, removePhase }: handlePhaseChangeParams) => void
  onGetDisabledDates: (date: moment.Moment, phaseIndex: number) => boolean
  onGetIsCurrentDateAllowed: (date: moment.Moment, phaseIndex: number) => boolean
  reValidatePhase: boolean
}

export const Phase = ({ phase, index, form, requiredFields, canAddNewPhase, canBeRemoved, savePhaseHandler, addPhaseHandler, removePhaseHandler, onPhaseChange, onGetDisabledDates, reValidatePhase, onGetIsCurrentDateAllowed }: PhaseProps) => {
  const [isPhaseValid, setIsPhaseValid] = useState(false)
  const [isPhaseSaved, setIsPhaseSaved] = useState(true)
  const [hadInvalidDate, setHadInvalidDate] = useState(false)
  const [canPhaseBeAdded, setCanPhaseBeAdded] = useState(false)
  const [clearIPAFields, setClearIPAFields] = useState(false)
  const [currentRequiredFields, setCurrentRequiredFields] = useState(requiredFields())

  const { getFieldDecorator, getFieldValue, getFieldsValue, getFieldError, validateFields, resetFields, isFieldsTouched, setFieldsValue } = form

  const phaseEnglishMessage = phase.messages?.find(message => message.languageId === englishLanguageId)

  useEffect(() => {
    validatePhase()
  }, [])

  useEffect(() => {
    if (reValidatePhase) {
      const newRequiredFields = requiredFields()
      setCurrentRequiredFields(newRequiredFields)

      const currentDate = getFieldValue(`date_${index}`)
      const hasInvalidDate = currentDate && !onGetIsCurrentDateAllowed(currentDate, index)

      if (hasInvalidDate) {
        setHadInvalidDate(hasInvalidDate)
        setFieldsValue({ [`date_${index}`]: null })
      }
    }
  }, [reValidatePhase])

  useEffect(() => {
    validatePhase()
  }, [currentRequiredFields, canAddNewPhase])

  useEffect(() => {
    onPhaseChange({ phaseIndex: index, isPhaseValid, isPhaseSaved })
  }, [isPhaseValid, isPhaseSaved])

  useEffect(() => {
    setIsPhaseSaved(true)
  }, [phase.userTypesL3, phase.endDate, phaseEnglishMessage])

  useEffect(() => {
    const fieldsToCheck = [
      `user_types_l3_${index}`,
      `date_${index}`,
      `embargoMessage_${index}`,
      `partner_roles_${index}`,
      `roles_${index}`,
      `tiers_${index}`,
      `specialties_${index}`,
      `regions_${index}`,
      `groups_${index}`,
      `accounts_${index}`,
      `request_permisson_${index}`
    ]

    const savedUserTypesL3 = phase.userTypesL3.map(userType => userType.id).toString()
    const currentUserTypesL3 = getFieldValue(`user_types_l3_${index}`).toString()

    const currentDate = getFieldValue(`date_${index}`)

    if (isFieldsTouched(fieldsToCheck) ||
      (savedUserTypesL3 !== currentUserTypesL3) ||
      (currentDate !== phase.endDate) ||
      (getFieldValue(`embargoMessage_${index}`) !== phaseEnglishMessage.description)
    ) {
      validatePhase()
      isPhaseSaved && setIsPhaseSaved(false)

      if ((currentDate && currentDate !== phase.endDate) && hadInvalidDate) {
        setHadInvalidDate(false)
      }
    } else {
      validatePhase()
      setIsPhaseSaved(true)
    }
  }, [
    getFieldValue(`user_types_l3_${index}`),
    getFieldValue(`date_${index}`),
    getFieldValue(`embargoMessage_${index}`),
    getFieldValue(`groups_${index}`),
    getFieldValue(`accounts_${index}`),
    getFieldValue(`request_permisson_${index}`)
  ])

  const validatePhase = () => {
    let phaseIsValid = false
    let canAddPhase = false
    validateFields(
      [`user_types_l3_${index}`,
      `date_${index}`,
      `embargoMessage_${index}`,
      `partner_roles_${index}`,
      `roles_${index}`,
      `tiers_${index}`,
      `specialties_${index}`,
      `regions_${index}`,
      `groups_${index}`,
      `accounts_${index}`,
      `request_permisson_${index}`
      ],
      { force: true },
      (errors, values) => {
        if (!errors) {
          const date = values[`date_${index}`]
          const userTypesL3 = values[`user_types_l3_${index}`]
          const embargoMessage = values[`embargoMessage_${index}`]

          const atLeastOneIsFilled = !(!date && !userTypesL3.length && !embargoMessage)
          const requiredForNewPhase = date && userTypesL3.length

          if (atLeastOneIsFilled) {
            phaseIsValid = true
          }
          if (requiredForNewPhase) {
            canAddPhase = true
          }
        }
      }
    )

    setIsPhaseValid(phaseIsValid)
    setCanPhaseBeAdded(canAddNewPhase && phaseIsValid && canAddPhase)
  }

  const handlePhaseCancel = () => {
    setClearIPAFields(true)

    resetFields([
      `user_types_l3_${index}`,
      `date_${index}`,
      `embargoMessage_${index}`
    ])

    setFieldsValue({
      [`user_types_l3_${index}`]: phase.userTypesL3.map(d => d.id ? d.id : d),
      [`date_${index}`]: phase.endDate,
      [`embargoMessage_${index}`]: phaseEnglishMessage?.description
    })

    setTimeout(() => {
      setClearIPAFields(false)
    }, 2000)
  }

  const handlePhaseSave = () => {
    const values = getFieldsValue([
      `user_types_l3_${index}`,
      `date_${index}`,
      `embargoMessage_${index}`,
      `partner_roles_${index}`,
      `roles_${index}`,
      `tiers_${index}`,
      `specialties_${index}`,
      `regions_${index}`,
      `groups_${index}`,
      `accounts_${index}`,
      `request_permisson_${index}`
    ])

    savePhaseHandler({
      id: phase.id || undefined,
      name: phase.name,
      startDate: phase.startDate,
      userTypesL3: values[`user_types_l3_${index}`],
      groups: values[`groups_${index}`],
      accounts: values[`accounts_${index}`],
      internalsGroup: values[`request_permisson_${index}`],
      endDate: values[`date_${index}`],
      messages: [
        {
          languageId: 1,
          description: values[`embargoMessage_${index}`]
        },
        ...phase.messages.filter(message => message.languageId !== englishLanguageId)
      ]
    })
  }

  const shouldAddPhasesBedisabled = () => {
    return !canPhaseBeAdded || !isPhaseSaved || (index !== 0 && !canBeRemoved)
  }

  return (
    <PhaseStyled>
      <PhaseFieldsContainer>
        <Text
          fontFamily='Poppins'
          color='#0071c5'
          size={14}
          margin='-5px 0 20px 0'
          weight='bold'
          lineheight='normal'
        >
          <u>At least one</u> of the following fields is required:
        </Text>

        <Form.Item label="Embargo Viewing Permissions" style={{ marginBottom: '10px' }} ref={undefined}>
          {getFieldDecorator(`user_types_l3_${index}`, {
            initialValue: phase.userTypesL3.map(d => d.id ? d.id : d),
            rules: [
              { required: currentRequiredFields.includes('user_types_l3') }
            ]
          })(
            <>
              <Text
                fontFamily='Roboto'
                color='#666674'
                size={12}
                margin='-10px -5px -5px 0'
                lineheight='normal'
                className='delete-error'
              >
                Select which user types are enabled to see this asset or campaign under embargo.
              </Text>
              <IPAPermissions3
                instance={index}
                selectedItems={phase.userTypesL3.map(d => d.id ? d.id : d)}
                selectedGroups={phase.groups}
                selectedAccounts={phase.accounts}
                selectedRequestPermission={phase.internalsGroup}
                material='embargo'
                fromEmbargo={true}
                isEmbargoForm={true}
                form={form}
                hasRP
                hasAccounts
                hasGroups
                onCancel={clearIPAFields}
              />
            </>
          )}
        </Form.Item>

        {
          hadInvalidDate &&
          <Tag closable onClose={() => setHadInvalidDate(false)}>
            The date you chose earlier was no longer valid, you can choose a new one
          </Tag>
        }
        <Form.Item label="Embargo End Date">
          {getFieldDecorator(`date_${index}`, {
            initialValue: phase.endDate,
            rules: [
              { required: currentRequiredFields.includes('date') }
            ]
          })(
            <DateTimePicker
              showTime
              style={{ width: '100%' }}
              format={'YYYY-MM-DD HH:mm:ss [(GMT)]'}
              disabledDate={(date: moment.Moment) => onGetDisabledDates(date, index)}
            />
          )}
        </Form.Item>

        <Form.Item
          label='Embargo Message (English)'
        >
          {getFieldDecorator(`embargoMessage_${index}`, {
            initialValue: phaseEnglishMessage?.description || '',
            rules: [
              { validator: customValidations.notEmpty },
              { required: currentRequiredFields.includes('embargoMessage') }
            ]
          })(
            <Input.TextArea placeholder='Enter a message to describe the embargo restrictions.' rows={6} />
          )}
        </Form.Item>
        {<HelpText text={getFieldError(`embargoMessage_${index}`) ? getFieldError(`embargoMessage_${index}`)[0] : 'Enter a message to describe the embargo restrictions.'} />}
      </PhaseFieldsContainer>

      <PhaseActions>
        <Tooltip
          placement="top"
          trigger="hover"
          title={
            isPhaseSaved
              ? 'There are no changes from the last time you saved'
              : ''
          }
        >
          <CustomButton disabled={isPhaseSaved} type="link" onClick={handlePhaseCancel}>Cancel</CustomButton>
        </Tooltip>

        <Tooltip
          placement="top"
          trigger="hover"
          title={
            (!isPhaseValid || isPhaseSaved)
              ? !isPhaseValid ? 'Please complete the required fields to create a valid phase' : 'There are no changes from the last time you saved'
              : ''
          }
        >
          <CustomButton disabled={!isPhaseValid || isPhaseSaved} onClick={handlePhaseSave}>Save</CustomButton>
        </Tooltip>

        {canBeRemoved && (
          <CustomButton type='danger' shape='round' onClick={removePhaseHandler}>
            <MinusIcon fill='#fff' />
            Remove embargo phase
          </CustomButton>
        )}
      </PhaseActions>

      {index !== 4 && (
        <AddPhaseContainer>
          <Tooltip
            placement="topLeft"
            trigger="hover"
            title={
              shouldAddPhasesBedisabled() && !isPhaseSaved
                ? 'Please save the phase before adding a new one'
                : ''
            }
          >
            <CustomButton
              disabled={shouldAddPhasesBedisabled()}
              onClick={() => addPhaseHandler(false)}
              type='link'
              color='#0071c5'
            >
              <PlusIcon size='14' fill={shouldAddPhasesBedisabled() ? '#a9a9aa' : '#0071c5'} />
              Add Phase {index + 2} (Blank)
            </CustomButton>
            <CustomButton
              disabled={shouldAddPhasesBedisabled()}
              onClick={() => addPhaseHandler(true)}
              type='link'
              color='#0071c5'
            >
              <PlusIcon size='14' fill={shouldAddPhasesBedisabled() ? '#a9a9aa' : '#0071c5'} />
              Add Phase {index + 2} (Replicating previous phase)
            </CustomButton>
          </Tooltip>

          <Text
            fontFamily='Roboto'
            color='#a9a9aa'
            size={14}
            margin='5px 0 0'
            lineheight='normal'
          >
            To add a new phase, please ensure the Embargo End date and Viewing Permissions for the previous phase are complete.
          </Text>
        </AddPhaseContainer>
      )}
    </PhaseStyled>
  )
}
