import styled, { css } from 'styled-components'
import FooterModalStyled from 'src/v2/sharedComponents/FooterModal/FooterModalStyled'
import { FormStyled } from 'src/v2/sharedComponentStyles/FormStyled'

interface PrimaryEmbargoFormStyledProps {
  formType?: 'create' | 'update'
}

export const PrimaryEmbargoFormStyled = styled.div<PrimaryEmbargoFormStyledProps>`

  ${FormStyled} {
    .ant-form-item:first-child {
      margin-bottom: 5px !important;
    }
    .ant-form-item-label {
      line-height: 1.5;
    }

    .ant-form-item {
      margin-bottom: 10px !important;
     
      &-partnerRoles {
        margin-bottom: 0px !important;
      }  
      .ant-calendar-picker {
        margin-bottom: 20px;
      }
    }
    
  }


  ${({ formType }) => formType === 'update' && css`
    ${FooterModalStyled} {
      width: calc(100% + 60px);
      margin: 0 -28px -27px;
      padding: 20px 28px;

      button {
        padding: 7px 30px !important;
      }
    }
  `}

`
