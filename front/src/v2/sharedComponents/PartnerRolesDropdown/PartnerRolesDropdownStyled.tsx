import styled from 'styled-components'

export const PartnerRolesDropdownStyled = styled.div`
  position: relative;
  width: 100%;
  max-width: 300px;
`

export const DropdownHeader = styled.div<{ disabled: boolean, isOpen: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 14px 0 10px;
  border: none;
  border-bottom: 1px solid #d9d9d9;
  border-radius: 0;
  background-color: ${props => props.disabled ? '#f5f5f5' : '#F4F5F8'};
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  max-width: 150px;
  min-height: 37px;
  transition: all 0.3s;

  &:hover {
    border-bottom-color: ${props => props.disabled ? '#d9d9d9' : '#999'};
  }

  &:focus {
    border-bottom-color: #40a9ff;
    outline: none;
  }

  .dropdown-text {
    flex: 1;
    color: ${props => props.disabled ? '#bfbfbf' : '#999'};
    font-size: 14px !important;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: "Poppins", sans-serif !important;

    &.placeholder {
      color: ${props => props.disabled ? '#bfbfbf' : '#999'};
    }

    &.selected {
      color: ${props => props.disabled ? '#bfbfbf' : '#333'};
    }
  }

  .dropdown-arrow {
    margin-left: 12px;
    transition: transform 0.3s;
    transform: ${props => props.isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
    color: #999;

    svg {
      display: block;
    }
  }
`

export const DropdownContainer = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1050;
  max-width: 150px;
  background: #F4F5F8;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  border-top-left-radius: 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
`

export const DropdownContent = styled.div`
  max-height: 120px;
  overflow-y: auto;
  padding: 0px 0;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`

export const CheckboxItem = styled.div`
  padding: 7px 10px 3px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: #cccccc5e;
  }

  &.partner-roles-checkbox .ant-checkbox-wrapper {
    width: 100%;
    margin: 0;
    pointer-events: none; /* Prevent checkbox wrapper from handling clicks */

    .ant-checkbox {
      pointer-events: auto;
      .ant-checkbox-inner {
        transform: scale(0.9) !important;
        border: 1px solid #58595B !important;
        border-radius: 4px !important;
        background-color: #F4F5F8 !important;
      }
    }

    .ant-checkbox-checked {
      &::after {
        background-color: #F4F5F8 !important;
      }
      .ant-checkbox-inner {
        background-color: #F4F5F8 !important;
        &::after {
          margin-left: 0.5px !important;
          border-color: #58595B !important;
        }
      }
    }

    span:last-child {
      font-size: 13px !important;
      color: #666674 !important;
      line-height: normal !important;
      font-style: normal !important;
      font-weight: 400 !important;
      font-family: "Poppins", sans-serif !important;
      padding-bottom: 2px !important;
      padding-left: 5px !important;
    }
  }
`

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 6px 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
`

export const CancelButton = styled.button`
  padding: 6px 12px;
  border: 1px solid #fafafa;
  border-radius: 4px;
  background-color: #fafafa;
  color: #666;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  

  &:hover {
    border-color: #e0e0e0;
  }

  &:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
  }

  &:active {
    transform: translateY(1px);
  }
`

export const SaveButton = styled.button`
  padding: 6px 12px;
  border: 1px solid #20c99a;
  border-radius: 4px;
  background-color: #1EC99A;
  color: white;
  font-size: 12px;
  
  cursor: pointer;
  transition: all 0.3s;
  

  &:hover {
    background-color: #0adea4;
    border-color: #0adea4;
  }

  &:focus {
    border-color: #20c99a;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    outline: none;
  }

  &:active {
    transform: translateY(1px);
    background-color: #389e0d;
    border-color: #389e0d;
  }
`
