import React, { useState, useRef, useEffect, useCallback } from 'react'
import { PartnerRolesDropdownStyled, DropdownContainer, DropdownHeader, DropdownContent, CheckboxItem, ButtonContainer, CancelButton, SaveButton } from './PartnerRolesDropdownStyled'
import { CheckBox } from '../CheckBox/CheckBox'

export interface PartnerRoleOption {
  label: string
  value: number
  key?: string
}

interface PartnerRolesDropdownProps {
  options: PartnerRoleOption[]
  selectedValues?: number[]
  value?: number[]
  onChange: (values: number[]) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

const PartnerRolesDropdown: React.FC<PartnerRolesDropdownProps> = ({
  options = [],
  selectedValues = [],
  value,
  onChange,
  placeholder = 'Select a role',
  disabled = false,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [tempSelectedValues, setTempSelectedValues] = useState<number[]>([])
  const dropdownRef = useRef<HTMLDivElement>(null)

  const currentValues = value || selectedValues

  const handleToggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen)
    }
  }

  const handleCheckboxChange = (value: number, checked: boolean) => {
    if (checked) {
      setTempSelectedValues(prev => [...prev, value])
    } else {
      setTempSelectedValues(prev => prev.filter(v => v !== value))
    }
  }

  const handleSave = () => {
    onChange(tempSelectedValues)
    setIsOpen(false)
  }

  const handleCancel = useCallback(() => {
    setTempSelectedValues(currentValues)
    setIsOpen(false)
  }, [currentValues])

  useEffect(() => {
    setTempSelectedValues(currentValues)
  }, [JSON.stringify(currentValues)])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
        handleCancel()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, handleCancel])

  const getDisplayText = () => {
    if (currentValues.length === 0) {
      return placeholder
    }
    if (currentValues.length === 1) {
      const option = options.find(opt => opt.value === currentValues[0])
      return option?.label || placeholder
    }
    const selectedOptions = options.filter(opt => currentValues.includes(opt.value))
    return selectedOptions.map(opt => opt.label).join(', ')
  }

  return (
    <PartnerRolesDropdownStyled ref={dropdownRef} className={className}>
      <DropdownHeader
        onClick={handleToggleDropdown}
        disabled={disabled}
        isOpen={isOpen}
      >
        <span className={`dropdown-text ${currentValues.length === 0 ? 'placeholder' : 'selected'}`}>
          {getDisplayText()}
        </span>
        <span className="dropdown-arrow">
          <svg width="14" height="8" viewBox="0 0 14 8" fill="none">
            <path d="M1 1L7 7L13 1" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </span>
      </DropdownHeader>

      {isOpen && (
        <DropdownContainer>
          <DropdownContent>
            {options.map((option) => (
              <CheckboxItem
                key={option.value}
                className="partner-roles-checkbox"
                onClick={() => handleCheckboxChange(option.value, !tempSelectedValues.includes(option.value))}
              >
                <CheckBox
                  checked={tempSelectedValues.includes(option.value)}
                  onChange={(e) => {
                    e.stopPropagation() // Prevent double triggering
                    handleCheckboxChange(option.value, e.target.checked)
                  }}
                  textSize={14}
                  textColor="#333"
                >
                  {option.label}
                </CheckBox>
              </CheckboxItem>
            ))}
          </DropdownContent>
          <ButtonContainer>
            <CancelButton onClick={handleCancel}>
              Cancel
            </CancelButton>
            <SaveButton onClick={handleSave}>
              Save
            </SaveButton>
          </ButtonContainer>
        </DropdownContainer>
      )}
    </PartnerRolesDropdownStyled>
  )
}

export default PartnerRolesDropdown
