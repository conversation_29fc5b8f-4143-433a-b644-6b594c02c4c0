import React from 'react'
import { Checkbox } from 'antd'
import { CheckboxProps } from 'antd/lib/checkbox'
import styled from 'styled-components'

interface CheckboxStyledProps extends CheckboxProps {
  textSize: number
  textColor: string
  underlineOnHover: boolean
}

const CheckboxStyled: React.FC<CheckboxStyledProps> = styled((props: CheckboxStyledProps) => <Checkbox {...props} />)`

  &.ant-checkbox-wrapper {
    display: flex;
    align-items: center;
    margin: 0;
    width: fit-content;

    span:last-child {
      font-size: ${props => props.textSize}px !important;
      color: ${props => props.textColor};
      line-height: 1.15;
      padding-bottom: 1px;

      &:hover {
        text-decoration: ${props => props.underlineOnHover ? 'underline' : 'none'};
      }
    }

    .ant-checkbox {
      .ant-checkbox-inner {
        transform: scale(1.13);
        border: 2px solid #00a3f6 !important
      }
    }

    .ant-checkbox-checked {
      &::after {
        background-color: #00a3f6 !important;
      }
      .ant-checkbox-inner {
        background-color: #00a3f6 !important;
        &::after {
          border-color: #fff !important;
          left: 18%;
        }
      }
    }
  }

  .ant-checkbox:hover::after, &:hover .ant-checkbox::after {
    visibility: hidden;
  }

`

export default CheckboxStyled
