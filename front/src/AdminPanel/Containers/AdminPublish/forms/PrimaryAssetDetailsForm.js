import React, { Component } from 'react'
import { Form, Input, Select, Radio, Button, Tooltip, Col, Row, Spin, DatePicker } from 'antd'
import { Fetch, isFuture } from '../../../../helpers'
import * as _ from 'lodash'
import * as moment from 'moment'
import 'moment/locale/en-gb'
import InfoIcon from './../../../../images/information.svg'
import SelectWithCategories from '../../../Components/Inputs/SelectWithCategories/SelectWithCategories'
import SelectWithLimitOptions from '../../../Components/Inputs/SelectWithLimitOptions'
import { filterIntelOwnersNone } from 'src/v2/helpers/filterIntelOwnersNone'
import { removeHtmlTags } from 'src/v2/helpers/removeHtmlTag'

// THIS FILE IS USED TO EDIT THE PRIMARY ASSET DETAILS

const { Option } = Select

const CustomizedLabel = (label) => <span className="upload-video-form__label">{label}</span>

class PrimaryAssetDetailsForm extends Component {
  constructor(props) {
    super(props)
    this.state = {
      MAX_LENGTH_TITLE: 150,
      confirmDirty: false,
      autoCompleteResult: [],
      loading: false,
      values: {
        system_name: undefined,
        upload_date: undefined,
        asset_types: undefined,
        file_formats: undefined,
        type_preview: undefined,
        dimensions: undefined,
        audiences: undefined,
        campaigns: undefined,
        intel_owner_id: undefined,
        products: undefined,
        devices: undefined,
        placements: undefined,
        brand_id: undefined,
        usage_reminder_id: undefined,
        tal_id: undefined,
        tags: undefined,
        programs: undefined
      },
      assetTypes: [],
      IPAGroupTypes: [],
      fileFormats: [],
      audiences: [],
      products: [],
      devices: [],
      programs: [],
      placements: [],
      brandMentions: [],
      usageReminders: [],
      tal: [],
      tags: [],
      campaings: [],
      intelOwners: []
    }

    this.myRef = React.createRef()
  }

  handleSubmit = (e) => {
    e.preventDefault()
    const {
      form: { validateFieldsAndScroll },
      onErrorsChange,
      getFormValues
    } = this.props

    validateFieldsAndScroll((err, values) => {
      if (!err) {
        values = this.mapper.formToServer({ ...values, active: 1 })
        getFormValues(values)
      } else {
        Object.keys(err).forEach((att) => {
          err[att] = err[att].errors
        })
        this.setState({ errors: { ...this.state.errors, ...err } })
      }

      onErrorsChange(err)
    })
  }

  filterByInput = (input, option) =>
    option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0

  addNewTag = (e) => {
    e.preventDefault()
    const {
      form: { getFieldValue, setFieldsValue }
    } = this.props
    const value = getFieldValue('newTag')
    this.fetch.newTag(value).then((tag) => {
      let { tags } = this.state
      const tagsSelected = getFieldValue('tags')
      tagsSelected.push(tag.id)
      setFieldsValue({
        tags: tagsSelected
      })
      tags = tags.concat(this.mapper.dataToCombobox([tag], 'id', 'id', 'description'))
      this.setState({ tags })
    })
  }

  componentDidMount() {
    const { data } = this.props
    if (data) {
      const values = this.mapper.dataToForm(data)
      this.setState({ values, loading: true })
    }
    const promises = []

    promises.push(this.fetch.assetTypes())
    promises.push(this.fetch.IPAGroupTypes())
    promises.push(this.fetch.fileFormats())
    promises.push(this.fetch.audiences())
    promises.push(this.fetch.products())
    promises.push(this.fetch.devices())
    promises.push(this.fetch.campaings())
    promises.push(this.fetch.intelOwners())
    promises.push(this.fetch.placements())
    promises.push(this.fetch.brandMentions())
    promises.push(this.fetch.usageReminders())
    promises.push(this.fetch.tal())
    promises.push(this.fetch.tags())
    promises.push(this.fetch.programs())

    Promise.all(promises).then((x) => {
      this.setState({ loading: false })
      this.props.form.validateFields({ suppressWarning: true })
    })
  }

  hasErrors(fieldsError) {
    return Object.keys(fieldsError).some((field) => fieldsError[field])
  }

  onValuesChange = () => {
    setTimeout(() => {
      const {
        form: { getFieldsError },
        onErrorsChange
      } = this.props
      this.hasErrors(getFieldsError()) ? onErrorsChange(true) : onErrorsChange(null)
    }, 200)
  }

  assetTypeToShow = (asset_types) => asset_types.filter((x) => !x.haveChild).map((x) => x.id)

  mapper = {
    dataToForm: (asset) => {
      return {
        ...asset,
        is_expired: asset.is_expired,
        upload_date: asset.upload_date
          ? moment(asset.upload_date, 'YYYY-MM-DD HH:mm:ss')
          : undefined,
        asset_types: this.assetTypeToShow(asset.asset_types),
        file_formats: asset.file_formats.map((x) => x.id),
        audiences: asset.audiences.map((x) => x.id),
        campaigns: asset.campaigns.map((x) => x.id),
        intel_owner_id: asset.intel_owner_id,
        products: asset.products.filter((x) => !x.haveChild).map((y) => y.id),
        devices: asset.devices.map((x) => x.id),
        placements: asset.placements.map((x) => x.id),
        brand_id: asset.brand_id || undefined,
        usage_reminder_id: asset.usage_reminder_id || undefined,
        tal_id: asset.tal_id || undefined,
        tags: asset.tags.map((x) => x.id),
        programs: asset.programs.map((x) => x.id)
      }
    },
    formToServer: (values) => {
      return {
        ...values,
        upload_date: values.upload_date
          ? moment(values.upload_date).format('YYYY-MM-DD HH:mm:ss')
          : null,
        file_formats: values.file_formats.length > 0 ? values.file_formats : [0],
        audiences: values.audiences.length > 0 ? values.audiences : [0],
        campaigns: values.campaigns.length > 0 ? values.campaigns : [0],
        products: values.products.length > 0 ? values.products : [0],
        devices: values.devices.length > 0 ? values.devices : [0],
        placements: values.placements.length > 0 ? values.placements : [0],
        brand_id: values.brand_id || 0,
        usage_reminder_id: values.usage_reminder_id || 0,
        tal_id: values.tal_id || 0,
        tags: values.tags.length > 0 ? values.tags : [0],
        programs: values.programs.length > 0 ? values.programs : [0]
      }
    },
    dataToCombobox: (data, keyAtt, valueAtt, labelAtt) => {
      if (!data) return []
      return data
        .map((x) => ({
          key: x[keyAtt],
          value: x[valueAtt],
          label: x[labelAtt],
          parent: x.parent ? x.parent : null
        }))
        .sort((a, b) => {
          const x = a.label
          const y = b.label
          return x < y ? -1 : x > y ? 1 : 0
        })
    },
    addChildToParent: (data) => {
      const headers = data.filter((x) => x.parent === null)
      const result = []
      headers.forEach((header) => {
        const child = data.filter((x) => x.parent === header.value)
        if (child.length === 0) {
          const copy = _.clone(header)
          copy.parent = header.value
          result.push(copy)
        }
      })
      return data.concat(result)
    }
  }

  fetch = {
    assetTypes: () => {
      return Fetch.GET('/assetType', (response) => {
        let assetTypes = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        assetTypes = this.mapper.addChildToParent(assetTypes)
        if (this.state.values && this.state.values.type_preview === 'bc') {
          const video = 3
          const productExplainerVideos = 616
          assetTypes = assetTypes
            .filter((x) => x.value === video || x.value === productExplainerVideos)
            .concat(assetTypes.filter((x) => x.parent === video))
        }
        this.setState({ assetTypes })
      })
    },
    IPAGroupTypes: () => {
      return Fetch.GET('/userIPAGroupTypes', (response) => {
        // const IPAGroupTypes = response.data
        // console.log('response', IPAGroupTypes);

        let IPAGroupTypes = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        IPAGroupTypes = this.mapper.addChildToParent(IPAGroupTypes)

        this.setState({ IPAGroupTypes })
      })
    },
    fileFormats: () => {
      return Fetch.GET('/fileFormat', (response) => {
        const fileFormats = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        fileFormats.map((format) => (format.label = format.label.toUpperCase()))
        this.setState({ fileFormats })
      })
    },
    audiences: () => {
      return Fetch.GET('/audience', (response) => {
        const audiences = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        this.setState({ audiences })
      })
    },
    programs: () => {
      return Fetch.GET('/programs', (response) => {
        const programs = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        this.setState({ programs })
      })
    },
    campaings: () => {
      return Fetch.GET('/campaign', (response) => {
        const campaings = this.mapper.dataToCombobox(response.data, 'id', 'id', 'system_name')
        this.setState({ campaings })
      })
    },
    intelOwners: () => {
      return Fetch.GET('/intelOwner', (response) => {
        const intelOwners = filterIntelOwnersNone(response.data).map((owner) => ({
          key: owner.id,
          value: owner.id,
          label: owner.name,
          active: owner.status
        }))

        this.setState({ intelOwners })
      })
    },
    products: () => {
      return Fetch.GET('/product', (response) => {
        let products = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        products = this.mapper.addChildToParent(products)
        this.setState({ products })
      })
    },
    devices: () => {
      return Fetch.GET('/device', (response) => {
        const devices = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        this.setState({ devices })
      })
    },
    placements: () => {
      return Fetch.GET('/placement', (response) => {
        const placements = this.mapper.dataToCombobox(response.data, 'id', 'id', 'description')
        this.setState({ placements })
      })
    },
    brandMentions: () => {
      return Fetch.GET('/admin/brandmention/search?showDetails=true', (response) => {
        const brandMentions = response.data.brands.map((brand) => {
          const brandMentionEnglish = brand.brand_language.find(
            (secondaryBrand) => secondaryBrand.language_id === 1
          )
          return {
            key: brand.id,
            value: brand.id,
            label: brandMentionEnglish.brand_mention,
            parent: brand.parent ? brand.parent : null
          }
        })
        this.setState({ brandMentions })
      })
    },
    usageReminders: () => {
      return Fetch.GET('/admin/usagereminder', (response) => {
        const usageReminders = this.mapper.dataToCombobox(response.data, 'id', 'id', 'title')
        this.setState({ usageReminders })
      }).catch(() => {
        this.setState({ usageReminders: [] })
      })
    },
    tal: () => {
      return Fetch.GET(`/admin/tal/search?showDetails=true`, (response) => {
        const tal = this.mapper.dataToCombobox(response.data.tals, 'id', 'id', 'description')
        this.setState({ tal })
      }).catch(() => {
        this.setState({ tal: [] })
      })
    },
    tags: () => {
      return Fetch.GET('/tags', (response) => {
        const sortedData = response.data.sort((a, b) => {
          return a.description.length - b.description.length
        })
        const tags = this.mapper.dataToCombobox(sortedData, 'id', 'id', 'description')
        this.setState({ tags })
      })
    },
    newTag: (tag) => {
      return Fetch.POST(`/tags?description=${tag}`, (response) => {
        if (response && response.data) {
          return response.data
        }
      })
    }
  }

  onCancel = () => {
    const {
      form: { resetFields }
    } = this.props
    resetFields()
  }

  render() {
    const { getFieldDecorator, getFieldsError, getFieldError, isFieldTouched } = this.props.form

    const {
      system_name,
      upload_date,
      asset_types,
      file_formats,
      type_preview,
      dimensions,
      audiences,
      campaigns,
      intel_owner_id,
      is_expired,
      products,
      devices,
      placements,
      brand_id,
      usage_reminder_id,
      tal_id,
      tags,
      programs
    } = this.state.values
    const {
      loading,
      assetTypes,
      IPAGroupTypes,
      fileFormats,
      audiences: audiencesCombo,
      products: productCombo,
      devices: devicesCombo,
      placements: placementsCombo,
      brandMentions,
      usageReminders,
      tal,
      tags: tagsCombo,
      campaings: campaingsCombo,
      intelOwners
    } = this.state

    const validationErrors = Object.values(getFieldsError())
    const hasError = validationErrors.some((e) => e !== undefined)
    const isTypeVideo = type_preview === 'bc'
    // Only show error after a field is touched.
    const validateFieldStatus = (field) => {
      return isFieldTouched(field) && getFieldError(field) ? 'error' : ''
    }
    if (loading) {
      return (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Spin />
        </div>
      )
    }
    const systemNameValidator = (rule, value, callback) => {
      const regExp = new RegExp(/\w+\.\s\w+/)
      if (regExp.test(value)) {
        callback(
          'Please check your input data. Try removing any blank spaces that appear after any punctuation marks. (e.g., "Intel. Core.").'
        )
      }
      callback()
    }

    return (
      <div ref={this.myRef}>
        <Form onSubmit={this.handleSubmit} onChange={this.onValuesChange}>
          <Row>
            <Col span={12}>
              <Form.Item
                className="item-with-tooltip"
                label={CustomizedLabel('Asset System Name')}
                validateStatus={validateFieldStatus('system_name')}
              >
                <p className="required">*Required</p>
                {getFieldDecorator('system_name', {
                  initialValue: system_name,
                  rules: [
                    {
                      required: true,
                      message: 'Asset System Name is required'
                    },
                    {
                      max: this.state.MAX_LENGTH_TITLE,
                      message: `Max. ${this.state.MAX_LENGTH_TITLE} characters.`
                    },
                    {
                      validator: systemNameValidator
                    }
                  ]
                })(<Input />)}
                <div className="answer">
                  <Tooltip
                    overlayClassName="new-admin-tootltip"
                    placement="right"
                    trigger="hover"
                    title="Enter a name to identify this asset in the admin panel."
                    getPopupContainer={() => this.myRef.current}
                  >
                    <Button type="primary" shape="circle">
                      {' '}
                      ?{' '}
                    </Button>
                  </Tooltip>
                </div>
              </Form.Item>

              <Form.Item label={'Upload Date'}>
                {getFieldDecorator('upload_date', {
                  initialValue: upload_date
                })(
                  <DatePicker
                    showTime
                    allowClear={false}
                    style={{ width: '100%' }}
                    format={'YYYY-MM-DD HH:mm:ss [(GMT)]'}
                    disabledDate={(current) => {
                      let formatedDate = moment(current).format('Y-MM-DD HH:mm:ss')
                      return isFuture(formatedDate)
                    }}
                  />
                )}
              </Form.Item>

              <Form.Item
                label={CustomizedLabel('Asset Type')}
                validateStatus={validateFieldStatus('asset_types')}
              >
                {getFieldDecorator('asset_types', {
                  initialValue: asset_types,
                  rules: [
                    {
                      required: true,
                      message: 'Asset Type are required'
                    }
                  ]
                })(
                  <SelectWithCategories
                    mode="multiple"
                    items={IPAGroupTypes}
                    showSearch
                    placeholder="Please select an Asset Type"
                    filterOption={(input, option) =>
                      !Array.isArray(option.props.children)
                        ? option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        : null
                    }
                  />
                )}
              </Form.Item>
              <Form.Item
                label={CustomizedLabel('File Formats')}
                help="You can choose more than one option."
                validateStatus={validateFieldStatus('file_formats')}
              >
                {getFieldDecorator('file_formats', {
                  initialValue: isTypeVideo ? [8] : file_formats,
                  rules: [
                    {
                      required: true,
                      message: 'File Formats are required'
                    }
                  ]
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    onChange={this.onValuesChange}
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                    disabled={isTypeVideo ? true : false}
                  >
                    {fileFormats &&
                      fileFormats.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              {type_preview !== 'bc' && (
                <Form.Item
                  label={CustomizedLabel('Asset Preview Format')}
                  validateStatus={validateFieldStatus('type_preview')}
                >
                  {getFieldDecorator('type_preview', {
                    initialValue: type_preview,
                    rules: [
                      {
                        required: true,
                        message: 'Asset Preview Format is required'
                      }
                    ]
                  })(
                    <Radio.Group>
                      <Radio value="image">Image</Radio>
                      <Radio value="html" className="ml-5">
                        HTML
                      </Radio>
                    </Radio.Group>
                  )}
                </Form.Item>
              )}
              <Form.Item
                label={isTypeVideo ? 'Video Dimensions' : 'Asset Dimensions'}
                help="Enter the asset dimensions in the suggested format."
                validateStatus={validateFieldStatus('dimensions')}
              >
                {getFieldDecorator('dimensions', {
                  initialValue: dimensions
                })(<Input />)}
              </Form.Item>
              <Form.Item
                label={CustomizedLabel('Audience')}
                help="You can choose more than one option."
                validateStatus={validateFieldStatus('audiences')}
              >
                {getFieldDecorator('audiences', {
                  initialValue: audiences,
                  rules: [
                    {
                      required: true,
                      message: 'Audience are required'
                    }
                  ]
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    onChange={this.onValuesChange}
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {audiencesCombo &&
                      audiencesCombo.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item
                label={'Funding Programs'}
                help="You can choose more than one option."
                className="funding-display-none"
                validateStatus={validateFieldStatus('programs')}
              >
                {getFieldDecorator('programs', {
                  initialValue: programs
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    onChange={this.onValuesChange}
                    showSearch
                    filterOption={(input, option) =>
                      !Array.isArray(option.props.children)
                        ? option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        : null
                    }
                  >
                    {!_.isEmpty(this.state.programs) &&
                      this.state.programs.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label.split(' ')[0]}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>

              <Form.Item
                label={'Campaign'}
                help="You can choose more than one option."
                validateStatus={validateFieldStatus('campaigns')}
              >
                {getFieldDecorator('campaigns', {
                  initialValue: campaigns
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    showSearch
                    filterOption={(input, option) => {
                      let string
                      if (input.includes('CA')) {
                        let split = input.split('CA')
                        string = split[1]
                      } else {
                        string = input
                      }

                      return (
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
                        option.key.toLowerCase().indexOf(string.toLowerCase()) >= 0
                      )
                    }}
                  >
                    {campaingsCombo &&
                      campaingsCombo.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item
                className={is_expired ? 'intel-owner-expired' : ''}
                label={CustomizedLabel('Intel Owner')}
                validateStatus={validateFieldStatus('intel_owner_id')}
              >
                {is_expired &&
                this.state.intelOwners.find(
                  (owner) => owner.value === intel_owner_id && !owner.active
                ) ? (
                  <span className="intel-owner-expired">
                    {this.state.intelOwners.find((owner) => owner.value === intel_owner_id)?.label}
                    <Tooltip
                      overlayClassName="new-admin-tootltip intel-owner-tooltip"
                      placement="right"
                      trigger="hover"
                      title="The asset is owned by a previous team member who is currently unavailable for direct contact."
                      getPopupContainer={() => this.myRef.current}
                    >
                      <Button className="info-icon" type="primary" shape="circle">
                        {' '}
                        i{' '}
                      </Button>
                    </Tooltip>
                  </span>
                ) : (
                  getFieldDecorator('intel_owner_id', {
                    initialValue: intel_owner_id,
                    rules: [
                      {
                        required: true,
                        message: 'Intel Owner is required'
                      }
                    ]
                  })(
                    <Select
                      className="single-select"
                      placeholder="Please select an option"
                      showSearch
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {this.state.intelOwners &&
                        this.state.intelOwners.find(
                          (owner) => owner.value === intel_owner_id && !owner.active
                        ) && (
                          <Option
                            key={
                              this.state.intelOwners.find((owner) => owner.value === intel_owner_id)
                                ?.key
                            }
                            value={
                              this.state.intelOwners.find((owner) => owner.value === intel_owner_id)
                                ?.value
                            }
                            disabled
                          >
                            {
                              this.state.intelOwners.find((owner) => owner.value === intel_owner_id)
                                ?.label
                            }
                          </Option>
                        )}

                      {intelOwners &&
                        intelOwners
                          .filter((owner) => owner.active)
                          .map((owner) => (
                            <Option key={owner.key} value={owner.value}>
                              {owner.label}
                            </Option>
                          ))}
                    </Select>
                  )
                )}
              </Form.Item>
              <Form.Item
                label={CustomizedLabel('Product')}
                help="You can choose more than one option."
                validateStatus={validateFieldStatus('products')}
              >
                {getFieldDecorator('products', {
                  initialValue: products,
                  rules: [
                    {
                      required: true,
                      message: 'Product is required'
                    }
                  ]
                })(
                  <SelectWithCategories
                    items={productCombo}
                    showSearch
                    mode="multiple"
                    placeholder="Please select an option"
                    filterOption={(input, option) =>
                      !Array.isArray(option.props.children)
                        ? option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        : null
                    }
                  />
                )}
              </Form.Item>

              <Form.Item
                label={CustomizedLabel('Device')}
                help="You can choose more than one option."
                validateStatus={validateFieldStatus('devices')}
              >
                {getFieldDecorator('devices', {
                  initialValue: devices,
                  rules: [
                    {
                      required: true,
                      message: 'Device are required'
                    }
                  ]
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {devicesCombo &&
                      devicesCombo.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item
                label={'Shopper Journey Stage/s'}
                help="You can choose more than one option."
                validateStatus={validateFieldStatus('placements')}
              >
                {getFieldDecorator('placements', {
                  initialValue: placements
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {placementsCombo &&
                      placementsCombo.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item
                label={'Brand Mention Associated'}
                validateStatus={validateFieldStatus('brand_id')}
              >
                {getFieldDecorator('brand_id', {
                  initialValue: brand_id
                })(
                  <Select
                    className="single-select"
                    allowClear
                    placeholder="Please select an option"
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {brandMentions &&
                      brandMentions.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {removeHtmlTags(f.label)}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item
                label={'Usage Reminder'}
                validateStatus={validateFieldStatus('usage_reminder_id')}
              >
                {getFieldDecorator('usage_reminder_id', {
                  initialValue: usage_reminder_id
                })(
                  <Select
                    className="single-select"
                    allowClear
                    placeholder="Please select an option"
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {usageReminders &&
                      usageReminders.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item
                label={'Trademark Acknowledgment Line'}
                validateStatus={validateFieldStatus('tal_id')}
              >
                {getFieldDecorator('tal_id', {
                  initialValue: tal_id
                })(
                  <Select
                    className="single-select"
                    allowClear
                    placeholder="Please select an option"
                    showSearch
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 || null
                    }
                  >
                    {tal &&
                      tal.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>
              <Form.Item
                label={'Tags'}
                help="You can choose more than one option."
                validateStatus={validateFieldStatus('tags')}
              >
                {getFieldDecorator('tags', {
                  initialValue: tags
                })(
                  <SelectWithLimitOptions
                    selectedData={tagsCombo}
                    mode="multiple"
                    maxShowCount={50}
                    placeholder="Please select an option"
                    option={{ valueKey: 'value', titleKey: 'label' }}
                  />
                )}
              </Form.Item>

              <Form.Item>
                {getFieldDecorator('newTag')(<Input placeholder="Add a new tag" />)}
              </Form.Item>
              <Button type="secondary" shape="round" onClick={this.addNewTag}>
                {' '}
                Add new tag{' '}
              </Button>
            </Col>
            <Col></Col>
          </Row>
          <Row>
            <Col>
              <Button size="large" className="admin__button cancel" onClick={this.onCancel}>
                {' '}
                Cancel{' '}
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                className="admin__button confirm"
                // disabled={hasError}
                disabled={this.hasErrors(getFieldsError())}
                loading={this.props.loading}
              >
                {' '}
                Save{' '}
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
    )
  }
}

export default Form.create({ name: 'primaryAssetDetailsForm' })(PrimaryAssetDetailsForm)
