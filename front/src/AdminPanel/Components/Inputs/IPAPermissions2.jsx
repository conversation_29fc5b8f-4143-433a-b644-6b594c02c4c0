import React, { useState, useEffect } from 'react'
import * as _ from 'lodash'
import { Form, Radio, Select, Spin, Tooltip, Button } from 'antd'
import './IPAPermissions.scss'
import New from './../../../images/New.png'
import CustomLabel from './CustomLabel'
import InfoIcon from './../../../images/information.svg'
import RedInfoIcon from './../../../images/information-red.svg'

import useIPAPermissions from '../../hooks/useIPAPermissions'
import { FilterInternalUser } from './FilterInternalUsers'
import SelectInfinite from 'src/v2/sharedComponents/SelectInfinite/SelectInfinite'
import PartnerRolesDropdown from 'src/v2/sharedComponents/PartnerRolesDropdown'

const { Option } = Select

const IPAPermissions2 = (props) => {
  const {
    selectedItems,
    form: { setFieldsValue, isFieldTouched, getFieldDecorator },
    hasRegion = true,
    hasAccounts = false,
    hasGroups = false,
    hasUnrestricted = false,
    hasRP = true,
    handleSelectedPermission,
    selectedRequestPermission,
    onCancel
  } = props

  const [dataPermissions, isDataLoading] = useIPAPermissions({
    hasGroups,
    hasAccounts,
    hasRP,
    selectedItems
  })
  const [data, setData] = useState({
    selectedL3: [],
    typesPartnerRoles: [],
    partnerRoles: [],
    typesRole: [],
    typesTier: [],
    typesRegions: [],
    typesGroups: [],
    typesAccounts: [],
    selectedRoles: [],
    selectedTiers: [],
    selectedGroups: [],
    selectedAccounts: [],
    selectedRegions: [],
    selectedPartnerRoles: [],
    typeOfUser: null,
    internalSelection: [20],
    isInitialized: false
  })

  useEffect(() => {
    if (onCancel) {
      setInitialSelections()
    }
  }, [onCancel])

  useEffect(() => {
    if (!isDataLoading && !data.isInitialized) {
      setInitialSelections()
    }
  }, [isDataLoading, dataPermissions])

  const setInitialSelections = () => {
    const {
      selectedItems,
      form: { setFieldsValue }
    } = props

    if (!dataPermissions) {
      return
    }

    const {
      partnerRoles = [],
      roles = [],
      tiers = [],
      regions = [],
      groups = [],
      accounts = []
    } = dataPermissions
    const filteredRoles = roles ? roles.filter((rol) => rol.value !== 53) : []

    const selectedRoles = roles ? mappers.selectedL3(selectedItems, roles) : []
    const selectedPartnerRoles = partnerRoles ? mappers.selectedL3(selectedItems, partnerRoles) : []
    const selectedTiers = tiers ? mappers.selectedL3(selectedItems, tiers) : []
    const selectedRegions = regions ? mappers.selectedL3(selectedItems, regions) : []
    const typeOfUser = !selectedItems.length
      ? 0
      : selectedItems.length === 1 && selectedItems[0] === 20
      ? 1
      : selectedItems.length && selectedItems[0] === 71
      ? 3
      : 2
    const internalSelection = [20]

    setData((prev) => ({
      ...prev,
      typesPartnerRoles: partnerRoles,
      partnerRoles,
      typesRole: filteredRoles,
      typesTier: tiers,
      typesRegions: regions,
      typesGroups: groups,
      typesAccounts: accounts,
      initialSelectedItems: props.selectedItems,
      selectedGroups: props.selectedGroups,
      selectedAccounts: props.selectedAccounts,
      selectedL3: selectedItems,
      selectedRoles,
      selectedTiers,
      selectedRegions,
      selectedPartnerRoles,
      internalUserGroups: selectedRequestPermission,
      typeOfUser,
      internalSelection,
      isInitialized: true
    }))

    setFieldsValue({
      roles: selectedRoles,
      tiers: selectedTiers,
      regions: selectedRegions,
      partner_roles: selectedPartnerRoles
    })
    props.setIsDataLoading && props.setIsDataLoading(false)
  }

  const mappers = {
    selectedL3: (selectedItems, permissions) => {
      if (!selectedItems || !permissions) return []
      try {
        return selectedItems.filter(
          (x) => Array.isArray(permissions) && permissions.some((p) => p.key === x)
        )
      } catch (error) {
        console.error('Error in selectedL3:', { selectedItems, permissions, error })
        return []
      }
    }
  }

  const onTypeOfUserChange = (e) => {
    const { value } = e.target
    const l3 = value === 1 ? [20] : value === 3 ? [71, 20] : []
    setData({
      ...data,
      typeOfUser: value,
      selectedL3: l3,
      selectedRoles: [],
      selectedTiers: [],
      selectedRegions: [],
      selectedGroups: [],
      selectedAccounts: []
    })
    if (handleSelectedPermission) {
      handleSelectedPermission(l3[0])
    }
    setFieldsValue({ user_types_l3: l3 })
  }

  // Función para obtener Content Types basados en Partner Roles seleccionados
  const getContentTypesFromPartnerRoles = (selectedPartnerRoleIds) => {
    if (!selectedPartnerRoleIds || selectedPartnerRoleIds.length === 0) {
      return []
    }

    console.log('Partner Role IDs seleccionados:', selectedPartnerRoleIds)
    console.log('data.typesPartnerRoles disponibles:', data.typesPartnerRoles)

    // Obtener todos los role_account_associations de los Partner Roles seleccionados
    const allAssociations = []
    selectedPartnerRoleIds.forEach(partnerRoleId => {
      const partnerRole = data.typesPartnerRoles.find(pr => pr.value === partnerRoleId)
      console.log(`Partner Role encontrado para ID ${partnerRoleId}:`, partnerRole)

      if (partnerRole && partnerRole.role_associations) {
        console.log(`Asociaciones para ${partnerRole.label}:`, partnerRole.role_associations)
        partnerRole.role_associations.forEach(association => {
          // Agregar todas las asociaciones, evitando duplicados globales por id (no por role_account_id)
          if (!allAssociations.find(a => a.id === association.id)) {
            allAssociations.push(association)
          }
        })
      } else {
        console.log(`No se encontraron role_associations para Partner Role ID ${partnerRoleId}`)
      }
    })

    console.log('Todas las asociaciones encontradas:', allAssociations)

    // Mapear a los valores que usa Content Type (id de las asociaciones)
    const contentTypeIds = allAssociations.map(association => association.name)
    console.log('Content Type IDs finales:', contentTypeIds)

    return contentTypeIds
  }

  // Función para obtener Content Types que deben removerse al deseleccionar un Partner Role
  const getContentTypesToRemove = (removedPartnerRoleId, remainingPartnerRoleIds) => {
    // Obtener Content Types del Partner Role removido
    const removedPartnerRole = data.typesPartnerRoles.find(pr => pr.value === removedPartnerRoleId)
    if (!removedPartnerRole || !removedPartnerRole.role_associations) {
      return []
    }

    const removedContentTypes = removedPartnerRole.role_associations.map(assoc => assoc.name)

    // Obtener Content Types de los Partner Roles que quedan seleccionados
    const remainingContentTypes = getContentTypesFromPartnerRoles(remainingPartnerRoleIds)

    // Solo remover los Content Types que NO están en los Partner Roles restantes
    const contentTypesToRemove = removedContentTypes.filter(ct => !remainingContentTypes.includes(ct))

    console.log('Partner Role removido:', removedPartnerRole.label)
    console.log('Content Types del Partner Role removido:', removedContentTypes)
    console.log('Content Types de Partner Roles restantes:', remainingContentTypes)
    console.log('Content Types a remover:', contentTypesToRemove)

    return contentTypesToRemove
  }

  const onChangeL3 = (key, value) => {
    const updatedData = { ...data, [key]: value }

    // Si se cambió Partner Roles, actualizar Content Type automáticamente
    if (key === 'selectedPartnerRoles') {
      const previousPartnerRoles = data.selectedPartnerRoles || []
      const currentPartnerRoles = value || []

      // Detectar si se agregaron o removieron Partner Roles
      const addedRoles = currentPartnerRoles.filter(role => !previousPartnerRoles.includes(role))
      const removedRoles = previousPartnerRoles.filter(role => !currentPartnerRoles.includes(role))

      console.log('Partner Roles anteriores:', previousPartnerRoles)
      console.log('Partner Roles actuales:', currentPartnerRoles)
      console.log('Partner Roles agregados:', addedRoles)
      console.log('Partner Roles removidos:', removedRoles)

      if (removedRoles.length > 0) {
        // Si se removieron Partner Roles, remover solo sus Content Types específicos
        let currentContentTypes = [...(data.selectedRoles || [])]

        removedRoles.forEach(removedRoleId => {
          const contentTypesToRemove = getContentTypesToRemove(removedRoleId, currentPartnerRoles)
          currentContentTypes = currentContentTypes.filter(ct => !contentTypesToRemove.includes(ct))
        })

        // Agregar Content Types de los nuevos Partner Roles (si los hay)
        if (addedRoles.length > 0) {
          const newContentTypes = getContentTypesFromPartnerRoles(addedRoles)
          newContentTypes.forEach(ct => {
            if (!currentContentTypes.includes(ct)) {
              currentContentTypes.push(ct)
            }
          })
        }

        updatedData.selectedRoles = currentContentTypes
      } else if (addedRoles.length > 0) {
        // Si solo se agregaron Partner Roles, agregar sus Content Types a los existentes
        const currentContentTypes = [...(data.selectedRoles || [])]
        const newContentTypes = getContentTypesFromPartnerRoles(addedRoles)

        console.log('Content Types actuales antes de agregar:', currentContentTypes)
        console.log('Nuevos Content Types a agregar:', newContentTypes)

        newContentTypes.forEach(ct => {
          if (!currentContentTypes.includes(ct)) {
            currentContentTypes.push(ct)
          }
        })

        updatedData.selectedRoles = currentContentTypes
      }

      console.log('Content Types finales:', updatedData.selectedRoles)

      // Actualizar también el form field
      setFieldsValue({ roles: updatedData.selectedRoles })
    }

    const l3 = getDataL3(key, updatedData[key])
    updatedData.selectedL3 = l3

    setData(updatedData)
    setFieldsValue({ user_types_l3: l3 })
  }

  // Función específica para manejar cambios manuales en Content Type
  const onChangeContentType = (value) => {
    const updatedData = { ...data, selectedRoles: value }
    const l3 = getDataL3('selectedRoles', value)
    updatedData.selectedL3 = l3

    setData(updatedData)
    setFieldsValue({ user_types_l3: l3 })
  }

  const getDataL3 = (key, value) => {
    const initialArray = [...data.internalSelection, ...value]
    const l3 = {
      selectedTiers: [...initialArray, ...data.selectedRegions, ...data.selectedRoles],
      selectedRoles: [...initialArray, ...data.selectedRegions, ...data.selectedTiers],
      selectedRegions: [...initialArray, ...data.selectedRoles, ...data.selectedTiers]
    }

    return l3[key]
  }

  const toolTips = {
    internalUserFilter: () => (
      <>
        Only users within an existing group of Internal Users are allowed to see this{' '}
        {props.material}. If none is selected, the {props.material} will have no restrictions.
        <br />
        <br />
        Users that are <u>not</u> part of this group <u>will have to request permission</u> in order
        to see the {props.material}.
      </>
    ),
    partnerRoles: () => (
      <>
      <span style={{ color: '#666674' }}>
        Preset criteria: Applying a Partner Role preset will automatically fill in the Content Type field, and you can adjust it manually afterward.
      </span>
        <br />
        <br />
        <span style={{ color: '#fc4c02' }}>
        If there were any previous selections in the Content Type field, they will be replaced with the preset values you select.
        </span>
      </>
    ),
    contentType: () => (
      <>
        This selection will determine wich partners are allowed to see this {props.material}t based
        on their role.
        <br />
        <br />
        Select all the content types that apply to this {props.material}. At least 1 type is
        required.
      </>
    ),
    contentMember: () => (
      <>
        This selection will determine wich partners are allowed to see this {props.material} based
        on their membership tier.
        <br />
        <br />
        Select all tiers that will see this {props.material}. At least 1 tier is required.
      </>
    ),
    contentGroups: () => (
      <>
        This selection will determine that only the users in within an existing group are allowed to
        see this {props.material}. If none is selected, the {props.material} will have no group
        restrictions.
        <br />
        <br />
        In order to view the {props.material}, the user must be within the scope of the main viewing
        permissions AND at the same time be included within the group.
      </>
    ),
    contentInfoAccount: () => (
      <>
        This selection will determine that only the users in the accounts selected are allowed to
        see this {props.material}. If none is selected, the {props.material} will have no account
        restrictions.
        <br />
        <br />
        In order to view the {props.material}, the user must be within the scope of the main viewing
        permissons AND at the same time be included within the account
      </>
    ),
    contentRegion: () => (
      <>
        This selection will determine that only the users in the accounts selected are allowed to
        see this {props.material}. If none is selected, the {props.material} will have no account
        restrictions.
        <br />
        <br />
        In order to view the {props.material}, the user must be within the scope of the main viewing
        permissons AND at the same time be included within the account
      </>
    )
  }

  return (
    <div className="viewing-permissions ipa-permissions">
      {data.isInitialized ? (
        <div>
          <Radio.Group onChange={(e) => onTypeOfUserChange(e)} value={data.typeOfUser}>
            <Radio value={1}>
              <b>Internal users only</b>(Intel blue/green badges)
            </Radio>
            <Radio value={2}>
              <b>Internal and external users</b>(Intel + partners)
            </Radio>
            {hasUnrestricted && (
              <Radio value={3}>
                <b>Unrestricted</b>(Public Asset - Registered or Unregistered users)
              </Radio>
            )}
          </Radio.Group>

          {data.typeOfUser === 1 && hasRP && (
            <div className="ipa-permissions-hideable">
              <h2>
                Select the user entitlement criteria for this {props.material}
                {!props.fromEmbargo && props.isEmbargoForm && ' under embargo'}:
              </h2>
              {hasRP && (
                <FilterInternalUser
                  data={data}
                  setData={setData}
                  getFieldDecorator={getFieldDecorator}
                  arrayOfGroups={dataPermissions.internalUsers.data}
                  toolTips={toolTips}
                  getDataL3={getDataL3}
                  setFieldsValue={setFieldsValue}
                />
              )}
            </div>
          )}
          {data.typeOfUser === 2 && (
            <div className="ipa-permissions-hideable">
              <h2>
                Select the user entitlement criteria for this {props.material}
                {!props.fromEmbargo && props.isEmbargoForm && ' under embargo'}:
              </h2>
              <div className="partner-roles-container" style={{ position: 'relative', marginBottom: '14px', width: '65%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span className="not-required-label">Partner Roles</span>
                  <Form.Item
                    style={{ marginBottom: 0, width: '190px' }}
                    className={`${
                      (data.selectedPartnerRoles.length || !isFieldTouched('partner_roles')) &&
                      'delete-error'
                    }`}
                  >
                    {getFieldDecorator('partner_roles', {
                      initialValue:
                        data.selectedPartnerRoles && data.selectedPartnerRoles.length
                          ? data.selectedPartnerRoles
                          : props.selectedRoles,
                      rules: [
                        {
                          required: false,
                          message: 'At least 1 role is required'
                        }
                      ]
                    })(
                      <PartnerRolesDropdown
                        options={data.typesPartnerRoles}
                        onChange={(value) => onChangeL3('selectedPartnerRoles', value)}
                        placeholder="Select a role"
                      />
                    )}
                  </Form.Item>
                  {toolTips.partnerRoles && (
                    <div className="info-groups">
                      <Tooltip overlayClassName="new-admin-tootltip" placement="right" trigger="hover" title={toolTips.partnerRoles}>
                        <Button className="btn-info-groups-tag" style={{ boxShadow: 'unset', paddingLeft: '0px' }}>
                          <img width="30" height="30" src={RedInfoIcon} alt="infoicon" />
                        </Button>
                      </Tooltip>
                    </div>
                  )}
                </div>
              </div>
              
              <Form.Item
                colon={false}
                label={
                  <CustomLabel required={true} label={'Content Type'} info={toolTips.contentType} />
                }
                className={`${
                  (data.selectedRoles.length || !isFieldTouched('roles')) && 'delete-error'
                }`}
              >
                {getFieldDecorator('roles', {
                  initialValue: data.selectedRoles,
                  rules: [
                    {
                      required: true,
                      message: 'At least 1 role is required'
                    }
                  ]
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    showSearch
                    onChange={onChangeContentType}
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {data.typesRole &&
                      data.typesRole.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                  </Select>
                )}
              </Form.Item>

              <Form.Item
                colon={false}
                label={
                  <CustomLabel
                    required={true}
                    label={'Membership Tier'}
                    info={toolTips.contentMember}
                  />
                }
                className={`${
                  (data.selectedTiers.length || !isFieldTouched('tiers')) && 'delete-error'
                }`}
              >
                {getFieldDecorator('tiers', {
                  initialValue: data.selectedTiers,
                  rules: [
                    {
                      required: true,
                      message: 'At least 1 tier is required'
                    }
                  ]
                })(
                  <Select
                    mode="multiple"
                    placeholder="Please select an option"
                    showSearch
                    onChange={(value) => onChangeL3('selectedTiers', value)}
                    filterOption={(input, option) =>
                      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {data.typesTier.map((f) => (
                      <Option key={f.key} value={f.value}>
                        {f.label}
                      </Option>
                    ))}
                  </Select>
                )}
              </Form.Item>

              {hasRegion && (
                <Form.Item
                  colon={false}
                  label={
                    <CustomLabel
                      required={true}
                      label={'Publish by region'}
                      info={toolTips.contentRegion}
                    />
                  }
                  className="delete-error"
                >
                  <img id="new-icon" src={New} alt="new-icon" />
                  {getFieldDecorator('regions', {
                    initialValue: data.selectedRegions,
                    rules: [{ required: true, message: 'At least 1 region is required' }]
                  })(
                    <Select
                      mode="multiple"
                      placeholder="Please select an option"
                      showSearch
                      onChange={(value) => onChangeL3('selectedRegions', value)}
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {data.typesRegions.map((f) => (
                        <Option key={f.key} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              )}

              {hasGroups && (
                <Form.Item
                  colon={false}
                  label={<CustomLabel label={'Publish by Group'} info={toolTips.contentGroups} />}
                  className="delete-error"
                >
                  <img id="new-icon" src={New} alt="new-icon" />

                  {getFieldDecorator('groups', {
                    initialValue: props.selectedGroups
                  })(
                    <Select
                      mode="multiple"
                      placeholder="Please select an option"
                      showSearch
                      onChange={(value) => {
                        setData({ ...data, selectedGroups: value })
                        setFieldsValue({ groups: value })
                      }}
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {data.typesGroups.map((f) => (
                        <Option key={f.value} value={f.value}>
                          {f.label}
                        </Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              )}
              {hasAccounts && (
                <Form.Item
                  wrapperCol={{ span: 13 }}
                  labelCol={{ span: 24 }}
                  colon={false}
                  label={
                    <CustomLabel label={'Publish by account'} info={toolTips.contentInfoAccount} />
                  }
                  className="delete-error"
                >
                  <img id="new-icon" src={New} alt="new-icon" />
                  {getFieldDecorator('accounts', {
                    initialValue: props.selectedAccounts
                  })(
                    <SelectInfinite
                      showArrow={true}
                      mode="multiple"
                      placeholder="Please select an option"
                      showSearch
                      onChange={(value) => {
                        setData({ ...data, selectedAccounts: value })
                        setFieldsValue({ accounts: value })
                      }}
                      optionsData={data.typesAccounts}
                    />
                  )}
                </Form.Item>
              )}
              {hasRP && (
                <FilterInternalUser
                  data={data}
                  setData={setData}
                  getFieldDecorator={getFieldDecorator}
                  arrayOfGroups={dataPermissions.internalUsers.data}
                  toolTips={toolTips}
                  getDataL3={getDataL3}
                />
              )}
            </div>
          )}
        </div>
      ) : (
        <div
          style={{ display: 'flex', justifyContent: 'center', width: '50%', alignItems: 'center' }}
        >
          <Spin />
        </div>
      )}
    </div>
  )
}

export default IPAPermissions2
