import { useEffect, useState } from 'react'
import { Fetch } from '../../helpers'

const useIPAPermissions = ({ hasGroups, hasAccounts }) => {
  const [permissions, setPermissions] = useState({
    roles: [],
    specialties: [],
    tiers: [],
    regions: [],
    requestpermission: [],
    partnerRoles: []
  })
  const [accounts, setAccounts] = useState([])
  const [groups, setGroups] = useState([])
  const [internalUsers, setInternalUser] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const userIPATypes = fetches.userIPATypes()
    const userIPAGroupTypes = fetches.userIPAGroupTypes()
    const groups = hasGroups ? fetches.getGroups() : null
    const accounts = hasAccounts ? fetches.getAccounts() : null
    const requestPermission = fetches.getRequestPermission()
    Promise.all([userIPATypes, userIPAGroupTypes, groups, accounts, requestPermission]).then(
      (_) => {
        setLoading(false)
      }
    )
  }, [])

  const mappers = {
    selectIPAUserTypesByKeyword: (data, keyword) => {
      const type = data.find((x) => x.keyword === keyword && x.available === 1)
      return type.association.filter((x) => x.available === 1)
    },
    dataToCombobox: (data, keyAtt, valueAtt, labelAtt) => {
      if (!data) return []
      return data
        .map((x) => ({
          key: x[keyAtt],
          value: x[valueAtt],
          label: x[labelAtt],
          parent: x.parent ? x.parent : null
        }))
        .sort((a, b) => {
          const x = a.label
          const y = b.label
          return x < y ? -1 : x > y ? 1 : 0
        })
    },
    selectedL3: (selectedItems, permissions) => {
      return selectedItems.filter((x) => permissions.map((x) => x.key).includes(x))
    }
  }

  const fetches = {
    userIPATypes: () => {
      return Fetch.GET('/userIPATypes', (response) => {
        if (response.data && !response.error) {
          const { data } = response

          const rolesTypes = mappers.selectIPAUserTypesByKeyword(data, 'role')
          const tiersTypes = mappers.selectIPAUserTypesByKeyword(data, 'tier')
          const specialtiesTypes = mappers.selectIPAUserTypesByKeyword(data, 'specialty')
          const regionsTypes = mappers.selectIPAUserTypesByKeyword(data, 'region')

          const roles = mappers.dataToCombobox(rolesTypes, 'id', 'id', 'name')
          const tiers = mappers.dataToCombobox(tiersTypes, 'id', 'id', 'name')
          const specialties = mappers.dataToCombobox(specialtiesTypes, 'id', 'id', 'name')
          const regions = mappers.dataToCombobox(regionsTypes, 'id', 'id', 'name')
          setPermissions({ roles, tiers, specialties, regions })
        }
      })
    },
    userIPAGroupTypes: () => {
      return Fetch.GET('/userIPAGroupTypes', (response) => {
        if (response.data && !response.error) {
          const { data } = response
          // const rolesGroupTypes = mappers.selectIPAUserTypesByKeyword(data, 'description')
          // const rolesGroup = mappers.dataToCombobox(rolesGroupTypes, 'id', 'id', 'name')
          // setPermissions({ roles, tiers, specialties, regions })

          const partnerRoles = data
            ? data.map((data) => {
                if (!data) return []
                return {
                  key: data.id,
                  id: data.id,
                  description: data.description,
                  enabled: data.enabled,
                  position: data.position,
                  role_associations: data.role_account_associations
                }
              })
            : []
          setPermissions((prevPermissions) => ({
            ...prevPermissions,
            partnerRoles: partnerRoles
          }))
        }
      })
    },
    getRequestPermission: () => {
      return Fetch.GET('/requestpermisson', (response) => {
        setInternalUser(response)
      })
    },
    getGroups: () => {
      return Fetch.GET('/group', (response) => {
        const { data } = response
        const groups = data
          ? data.map((data) => {
              if (!data) return []
              return {
                key: data.id,
                value: data.id,
                label: data.name
              }
            })
          : []
        setGroups(groups)
      })
    },
    getAccounts: () => {
      return Fetch.GET('/accounts', (response) => {
        const { data } = response
        const accounts = data ? data.map((x) => ({ value: x.id, label: x.name })) : []
        setAccounts(accounts)
      })
    }
  }
  return [{ ...permissions, accounts, groups, internalUsers }, loading]
}

export default useIPAPermissions
